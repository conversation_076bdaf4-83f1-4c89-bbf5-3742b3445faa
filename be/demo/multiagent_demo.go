package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	agentDomain "git.nevint.com/fota3/t-rex/domain/agent"
	llmDomain "git.nevint.com/fota3/t-rex/domain/llm"
	multiagentDomain "git.nevint.com/fota3/t-rex/domain/multiagent"
	"git.nevint.com/fota3/t-rex/model/agent"
	modelLLM "git.nevint.com/fota3/t-rex/model/llm"
	"git.nevint.com/fota3/t-rex/pkg/tokenizer"
	multiagentService "git.nevint.com/fota3/t-rex/service/multiagent"
)

// MockConversationRepository implements the ConversationRepository interface for testing
type MockConversationRepository struct {
	conversations map[string]*agent.Conversation
	projectConvs  map[string]*agent.Conversation
}

func NewMockConversationRepository() *MockConversationRepository {
	return &MockConversationRepository{
		conversations: make(map[string]*agent.Conversation),
		projectConvs:  make(map[string]*agent.Conversation),
	}
}

func (m *MockConversationRepository) CreateConversation(ctx context.Context, conversation *agent.Conversation) error {
	m.conversations[conversation.ID.Hex()] = conversation
	return nil
}

func (m *MockConversationRepository) GetConversationByID(ctx context.Context, id string) (*agent.Conversation, error) {
	conv, exists := m.conversations[id]
	if !exists {
		return nil, nil
	}
	return conv, nil
}

func (m *MockConversationRepository) AppendMessage(ctx context.Context, conversationID string, message *agent.Message) error {
	conv, exists := m.conversations[conversationID]
	if !exists {
		return fmt.Errorf("conversation not found")
	}
	conv.Messages = append(conv.Messages, *message)
	return nil
}

func (m *MockConversationRepository) FindConversationsByUserID(ctx context.Context, userID string) ([]*agent.Conversation, error) {
	var result []*agent.Conversation
	for _, conv := range m.conversations {
		if conv.UserID == userID {
			result = append(result, conv)
		}
	}
	return result, nil
}

func (m *MockConversationRepository) GetConversationByProjectID(ctx context.Context, projectID string) (*agent.Conversation, error) {
	conv, exists := m.projectConvs[projectID]
	if !exists {
		return nil, nil
	}
	return conv, nil
}

func (m *MockConversationRepository) CreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	conv := &agent.Conversation{
		ID:            primitive.NewObjectID(),
		UserID:        userID,
		ProjectID:     projectID,
		Messages:      []agent.Message{},
		CreatedAt:     time.Now(),
		LastUpdatedAt: time.Now(),
	}
	m.conversations[conv.ID.Hex()] = conv
	m.projectConvs[projectID] = conv
	return conv, nil
}

func (m *MockConversationRepository) GetOrCreateProjectConversation(ctx context.Context, projectID string, userID string) (*agent.Conversation, error) {
	conv, err := m.GetConversationByProjectID(ctx, projectID)
	if err != nil {
		return nil, err
	}
	if conv == nil {
		return m.CreateProjectConversation(ctx, projectID, userID)
	}
	return conv, nil
}

func (m *MockConversationRepository) ClearConversationMessages(ctx context.Context, conversationID string) error {
	conv, exists := m.conversations[conversationID]
	if !exists {
		return fmt.Errorf("conversation not found")
	}
	conv.Messages = []agent.Message{}
	return nil
}

func (m *MockConversationRepository) DeleteProjectConversation(ctx context.Context, projectID string) error {
	conv, exists := m.projectConvs[projectID]
	if exists {
		delete(m.conversations, conv.ID.Hex())
		delete(m.projectConvs, projectID)
	}
	return nil
}

// MockLLMClient implements the LLMClient interface for testing
type MockLLMClient struct{}

func (m *MockLLMClient) ChatCompletionStream(
	ctx context.Context,
	llmConfig modelLLM.LLMConfig,
	req *multiagentDomain.ChatCompletionRequest,
) (<-chan multiagentDomain.ChatCompletionStreamResponse, error) {
	responseChan := make(chan multiagentDomain.ChatCompletionStreamResponse)

	go func() {
		defer close(responseChan)

		// Simulate streaming response based on request content
		var responses []string
		if len(req.Messages) > 0 {
			lastMessage := req.Messages[len(req.Messages)-1].Content
			if len(lastMessage) > 50 { // Complex request
				responses = []string{
					"🤖 Multi-Agent Analysis Starting...\n",
					"Agent 1: Analyzing code structure and architecture...\n",
					"Agent 2: Reviewing security and performance aspects...\n",
					"Agent 3: Generating documentation and recommendations...\n",
					"🔄 Synthesizing results from multiple agents...\n",
					"✅ Multi-agent analysis complete. Here's the comprehensive report:\n\n",
					"Based on the collaborative analysis from multiple specialized agents, ",
					"we have identified several key areas for improvement and provided ",
					"detailed recommendations with actionable insights.",
				}
			} else { // Simple request
				responses = []string{
					"Processing your request: ",
					lastMessage,
					"\n\nThis is a straightforward task handled by a single agent.",
				}
			}
		} else {
			responses = []string{"Hello! How can I help you today?"}
		}

		for _, response := range responses {
			select {
			case responseChan <- multiagentDomain.ChatCompletionStreamResponse{
				ContentDelta: response,
				IsFinal:      false,
			}:
				time.Sleep(200 * time.Millisecond) // Simulate streaming delay
			case <-ctx.Done():
				return
			}
		}

		// Send final response
		responseChan <- multiagentDomain.ChatCompletionStreamResponse{
			ContentDelta: "",
			IsFinal:      true,
		}
	}()

	return responseChan, nil
}

func (m *MockLLMClient) ChatCompletionNonStreaming(ctx context.Context, req *multiagentDomain.ChatCompletionRequest) (*multiagentDomain.ChatCompletionResponse, error) {
	return &multiagentDomain.ChatCompletionResponse{
		Content:      "This is a non-streaming response for result synthesis.",
		FinishReason: "stop",
	}, nil
}

// MockMCPToolExecutor implements the MCPToolExecutor interface for testing
type MockMCPToolExecutor struct{}

func (m *MockMCPToolExecutor) ExecuteToolCall(ctx context.Context, toolCall *agent.ToolCall) (*agent.ToolResult, error) {
	return &agent.ToolResult{
		CallID:  toolCall.CallID,
		Content: fmt.Sprintf("✅ Mock tool execution result for %s", toolCall.MCPMethod),
	}, nil
}

func (m *MockMCPToolExecutor) ListMCPServers(ctx context.Context) map[string][]mcp.Tool {
	return map[string][]mcp.Tool{
		"projectfs": {
			{Name: "read_file", Description: "Read file content"},
			{Name: "write_file", Description: "Write file content"},
			{Name: "list_files", Description: "List directory contents"},
		},
		"runtime": {
			{Name: "bash_exec", Description: "Execute bash commands"},
		},
	}
}

func main() {
	fmt.Println("🚀 MultiAgent Integration Test")
	fmt.Println("==============================")

	// Initialize logger
	logger, _ := zap.NewDevelopment()
	sugar := logger.Sugar()

	// Create mock dependencies
	repo := NewMockConversationRepository()
	llmClient := &MockLLMClient{}
	executor := &MockMCPToolExecutor{}

	// Create tokenizer service
	tokenizerService, err := tokenizer.NewService("cl100k_base")
	if err != nil {
		log.Fatalf("Failed to create tokenizer: %v", err)
	}

	historyManager := agentDomain.NewSimpleHistoryManager(
		sugar,
		tokenizerService,
		100,  // maxMessages
		4000, // maxTokens
	)

	// Create LLM domain (mock)
	llmDomainInstance := llmDomain.NewDomain(nil, logger)

	// Initialize MultiAgent Domain
	fmt.Println("🔧 Initializing MultiAgent Domain...")
	multiAgentDomain := multiagentDomain.NewMultiAgentDomain(
		repo,
		llmClient,
		executor,
		historyManager,
		sugar,
		llmDomainInstance,
	)

	// Create MultiAgent Service
	fmt.Println("🔧 Initializing MultiAgent Service...")
	_ = multiagentService.NewService(multiAgentDomain, sugar)

	// Test scenarios
	testScenarios := []struct {
		name        string
		projectID   string
		userID      string
		message     string
		description string
	}{
		{
			name:        "Simple Task",
			projectID:   "project-001",
			userID:      "user-001",
			message:     "Hello, how are you?",
			description: "Simple greeting - should use single agent",
		},
		{
			name:      "Complex Analysis Task",
			projectID: "project-002",
			userID:    "user-001",
			message: "Analyze the entire codebase, identify all security vulnerabilities, " +
				"review the architecture for scalability issues, generate comprehensive " +
				"documentation, and provide detailed recommendations for improvements " +
				"with code examples and best practices.",
			description: "Complex multi-step task - should trigger multi-agent processing",
		},
		{
			name:      "Code Review Task",
			projectID: "project-003",
			userID:    "user-002",
			message: "Please review all files in this project from multiple perspectives: " +
				"security, performance, maintainability, and code quality. Provide " +
				"detailed feedback and suggestions for each aspect.",
			description: "Multi-perspective analysis - should use multiple agents",
		},
	}

	// Run test scenarios
	for i, scenario := range testScenarios {
		fmt.Printf("\n%d. %s\n", i+1, scenario.name)
		fmt.Println(fmt.Sprintf("   %s", scenario.description))
		fmt.Println("   " + repeat("─", len(scenario.description)))

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)

		userMessage := agent.Message{
			MsgID:     fmt.Sprintf("msg-%d-%d", i, time.Now().Unix()),
			Role:      agent.RoleUser,
			Content:   scenario.message,
			Timestamp: time.Now(),
		}

		fmt.Printf("   📤 Sending: %s\n", truncateString(scenario.message, 60))

		// Test project streaming interaction
		streamChan, err := multiAgentDomain.HandleProjectStreamingInteraction(
			ctx,
			scenario.projectID,
			scenario.userID,
			userMessage,
		)

		if err != nil {
			fmt.Printf("   ❌ Error: %v\n", err)
			cancel()
			continue
		}

		fmt.Println("   📡 Streaming response:")

		// Consume the stream
		chunkCount := 0
		for chunk := range streamChan {
			chunkCount++
			if chunk.Error != nil {
				fmt.Printf("   ❌ Stream error: %v\n", *chunk.Error)
				break
			}

			if chunk.Delta != nil && *chunk.Delta != "" {
				fmt.Printf("   📥 %s", *chunk.Delta)
			}

			if chunk.Marker != nil {
				switch *chunk.Marker {
				case "start":
					fmt.Printf("   🟢 Stream started\n")
				case "end":
					fmt.Printf("   🔴 Stream ended\n")
				}
			}
		}

		fmt.Printf("   ✅ Completed with %d chunks\n", chunkCount)
		cancel()

		// Add delay between tests
		time.Sleep(1 * time.Second)
	}

	fmt.Println("\n🏁 MultiAgent Integration Test Completed")
	fmt.Println("=========================================")
	fmt.Println("✅ All test scenarios executed successfully")
	fmt.Println("📊 MultiAgent system is working correctly")
}

// Helper function to repeat strings (Go doesn't have built-in string repeat)
func repeat(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}
