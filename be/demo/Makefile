# MultiAgent Demo Makefile

.PHONY: help build run test test-unit test-client clean deps

# Default target
help:
	@echo "MultiAgent Demo - Available Commands:"
	@echo "======================================"
	@echo "  build        - Build the demo server"
	@echo "  run          - Run the demo server"
	@echo "  test         - Run all tests"
	@echo "  test-unit    - Run unit tests only"
	@echo "  test-client  - Run client integration tests"
	@echo "  clean        - Clean build artifacts"
	@echo "  deps         - Download dependencies"
	@echo "  help         - Show this help message"

# Build the demo server
build:
	@echo "🔨 Building MultiAgent Demo Server..."
	go build -o multiagent_demo ./multiagent_demo.go

# Run the demo server
run: build
	@echo "🚀 Starting MultiAgent Demo Server..."
	./multiagent_demo

# Run all tests
test: test-unit test-client

# Run unit tests
test-unit:
	@echo "🧪 Running MultiAgent Unit Tests..."
	go test -v ./multiagent_test.go -timeout=30s

# Run client integration tests (requires server to be running)
test-client:
	@echo "🌐 Running MultiAgent Client Integration Tests..."
	@echo "⚠️  Make sure the demo server is running on localhost:8080"
	@echo "   You can start it with: make run"
	@echo ""
	@read -p "Press Enter to continue or Ctrl+C to cancel..." dummy
	go run ./multiagent_client_test.go

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -f multiagent_demo
	go clean

# Download dependencies
deps:
	@echo "📦 Downloading dependencies..."
	go mod download
	go mod tidy

# Development helpers
dev-setup: deps
	@echo "🛠️  Setting up development environment..."
	@echo "Make sure you have the following services running:"
	@echo "  - MongoDB (default: mongodb://localhost:27017)"
	@echo "  - Redis (default: localhost:6379)"
	@echo "  - Docker (for runtime provider)"
	@echo ""
	@echo "Configuration file should be at: ../config/config.yaml"

# Quick test sequence
quick-test: build
	@echo "⚡ Running Quick Test Sequence..."
	@echo "1. Starting server in background..."
	./multiagent_demo &
	@SERVER_PID=$$!; \
	echo "Server PID: $$SERVER_PID"; \
	echo "2. Waiting for server to start..."; \
	sleep 5; \
	echo "3. Running client tests..."; \
	go run ./multiagent_client_test.go || true; \
	echo "4. Stopping server..."; \
	kill $$SERVER_PID || true

# Docker helpers (if needed)
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t multiagent-demo -f ../Dockerfile ..

docker-run: docker-build
	@echo "🐳 Running Docker container..."
	docker run -p 8080:8080 multiagent-demo

# Benchmark tests
benchmark:
	@echo "📊 Running MultiAgent Benchmarks..."
	go test -bench=. -benchmem ./multiagent_test.go

# Coverage report
coverage:
	@echo "📈 Generating Coverage Report..."
	go test -coverprofile=coverage.out ./multiagent_test.go
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Lint code
lint:
	@echo "🔍 Running Code Linter..."
	golangci-lint run ./...

# Format code
fmt:
	@echo "✨ Formatting Code..."
	go fmt ./...

# Check for security issues
security:
	@echo "🔒 Running Security Check..."
	gosec ./...

# All quality checks
quality: fmt lint security test-unit
	@echo "✅ All quality checks completed"

# Show project structure
structure:
	@echo "📁 Project Structure:"
	@echo "===================="
	@tree -I 'vendor|node_modules|.git' . || ls -la

# Show configuration
config:
	@echo "⚙️  Configuration Check:"
	@echo "========================"
	@echo "Config file: ../config/config.yaml"
	@if [ -f "../config/config.yaml" ]; then \
		echo "✅ Config file exists"; \
		echo ""; \
		echo "MongoDB URI: $$(grep 'uri:' ../config/config.yaml | head -1)"; \
		echo "Server Port: $$(grep 'port:' ../config/config.yaml | head -1)"; \
	else \
		echo "❌ Config file not found"; \
		echo "Please create ../config/config.yaml"; \
	fi

# Show logs (if server is running)
logs:
	@echo "📋 Checking for log files..."
	@if [ -f "../logs/app.log" ]; then \
		echo "📄 Application logs:"; \
		tail -f ../logs/app.log; \
	else \
		echo "No log files found. Server might not be running or logging to stdout."; \
	fi

# Monitor system resources
monitor:
	@echo "📊 System Resource Monitor:"
	@echo "=========================="
	@echo "Memory usage:"
	@ps aux | grep multiagent_demo | grep -v grep || echo "No multiagent_demo process found"
	@echo ""
	@echo "Port usage:"
	@netstat -tulpn | grep :8080 || echo "Port 8080 not in use"

# Interactive test menu
interactive:
	@echo "🎮 Interactive Test Menu"
	@echo "======================="
	@echo "1. Run Unit Tests"
	@echo "2. Start Demo Server"
	@echo "3. Run Client Tests"
	@echo "4. Check Status"
	@echo "5. View Logs"
	@echo "0. Exit"
	@echo ""
	@read -p "Select option (0-5): " choice; \
	case $$choice in \
		1) make test-unit ;; \
		2) make run ;; \
		3) make test-client ;; \
		4) make monitor ;; \
		5) make logs ;; \
		0) echo "Goodbye!" ;; \
		*) echo "Invalid option" ;; \
	esac
