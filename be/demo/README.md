# MultiAgent Service Demo

This demo showcases the MultiAgent service implementation for the T-Rex project. It demonstrates how the multi-agent architecture works, including task detection, concurrent agent execution, and result synthesis.

## 🏗️ Architecture Overview

The MultiAgent service implements a sophisticated multi-agent architecture that can intelligently decide whether to use single-agent or multi-agent processing based on the complexity of the user's request.

### Key Components

1. **TaskDetector** - Analyzes user input to determine if multi-agent processing is needed
2. **SubAgentManager** - Creates and manages individual agent instances
3. **ConcurrentExecutor** - Handles parallel execution of multiple agents
4. **ResultSynthesizer** - Combines results from multiple agents into a coherent response
5. **PermissionValidator** - Ensures agents operate within allowed boundaries
6. **ResourceMonitor** - Tracks resource usage and prevents abuse

### Multi-Agent Decision Logic

The system uses multi-agent processing when:
- Complex analysis tasks are requested
- Multiple perspectives are needed
- Code review or comprehensive analysis is required
- Tasks involve multiple steps or domains

Single-agent processing is used for:
- Simple file operations
- Basic queries
- Single-step tasks

## 🚀 Getting Started

### Prerequisites

1. **Go 1.23+** - Make sure Go is installed
2. **MongoDB** - Running on default port (27017)
3. **Redis** - Running on default port (6379)
4. **Docker** - For runtime provider (optional)

### Configuration

1. Ensure the configuration file exists at `../config/config.yaml`
2. Update MongoDB and Redis connection settings if needed
3. Configure LLM settings (OpenAI compatible API)

### Running the Demo

#### Option 1: Using Makefile (Recommended)

```bash
# Show available commands
make help

# Set up dependencies
make deps

# Build and run the server
make run

# In another terminal, run client tests
make test-client

# Run unit tests
make test-unit
```

#### Option 2: Manual Commands

```bash
# Build the demo server
go build -o multiagent_demo ./multiagent_demo.go

# Run the server
./multiagent_demo

# In another terminal, run tests
go test -v ./multiagent_test.go
go run ./multiagent_client_test.go
```

## 🧪 Testing

### Unit Tests

The unit tests (`multiagent_test.go`) cover:

- MultiAgent domain initialization
- Task detection logic
- Streaming interactions
- Project-level conversations
- Mock implementations for testing

```bash
make test-unit
```

### Integration Tests

The client tests (`multiagent_client_test.go`) demonstrate:

- User registration and authentication
- Sending messages to the MultiAgent service
- Receiving streaming responses
- Conversation history management
- Health checks and status monitoring

```bash
make test-client
```

## 📡 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register a new user
- `POST /api/v1/auth/login` - Login user
- `GET /api/v1/auth/profile` - Get user profile
- `POST /api/v1/auth/logout` - Logout user

### MultiAgent Service
- `POST /api/v1/multiagent/projects/{projectId}/conversation/messages` - Send message (SSE stream)
- `GET /api/v1/multiagent/projects/{projectId}/conversation/history` - Get conversation history
- `DELETE /api/v1/multiagent/projects/{projectId}/conversation/history` - Clear conversation
- `GET /api/v1/multiagent/projects/{projectId}/multiagent/status` - Get multi-agent status
- `POST /api/v1/multiagent/projects/{projectId}/multiagent/abort` - Abort execution
- `POST /api/v1/multiagent/projects/{projectId}/conversation/messages/{messageId}/retry` - Retry message
- `GET /api/v1/multiagent/health` - Health check
- `GET /api/v1/multiagent/metrics` - System metrics

## 🔧 Configuration

### Environment Variables

The demo uses the same configuration system as the main T-Rex application:

```yaml
# config/config.yaml
server:
  port: "8080"

mongodb:
  uri: "mongodb://localhost:27017"
  database: "trex_demo"

redis:
  address: "localhost:6379"
  password: ""

openai:
  api_key: "your-api-key"
  api_base: "https://api.openai.com/v1"
  model_name: "gpt-4"

workspace:
  base_path: "./demo_workspace"

mcp:
  servers_config_path: "../config/mcp_servers.json"
```

## 🎯 Test Scenarios

### Simple Tasks (Single Agent)
- "read file main.go"
- "show file content"
- "fix typo in line 10"

### Complex Tasks (Multi Agent)
- "analyze the entire codebase and create comprehensive documentation"
- "review all files and provide detailed feedback from multiple perspectives"
- "refactor the code, update tests, and create documentation"

## 📊 Monitoring

### Health Checks
```bash
curl http://localhost:8080/api/v1/multiagent/health
```

### System Metrics
```bash
curl http://localhost:8080/api/v1/multiagent/metrics
```

### Resource Monitoring
```bash
make monitor
```

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   - Ensure MongoDB is running: `mongod`
   - Check connection string in config

2. **Redis Connection Failed**
   - Ensure Redis is running: `redis-server`
   - Check Redis configuration

3. **LLM API Errors**
   - Verify API key configuration
   - Check network connectivity
   - Ensure API base URL is correct

4. **Port Already in Use**
   - Change port in config file
   - Kill existing processes: `lsof -ti:8080 | xargs kill`

### Debug Mode

Enable debug logging by setting log level to "debug" in config:

```yaml
logger:
  level: "debug"
  path: "./logs/multiagent_demo.log"
```

## 🔍 Code Structure

```
demo/
├── multiagent_demo.go      # Main demo server
├── multiagent_test.go      # Unit tests
├── multiagent_client_test.go # Integration tests
├── Makefile               # Build and test commands
└── README.md             # This file
```

## 🚀 Advanced Usage

### Custom Agent Configuration

You can modify the MultiAgent domain configuration:

```go
domain := multiagentDomain.NewMultiAgentDomain(
    repo,
    llmClient,
    executor,
    historyManager,
    logger,
    llmDomainInstance,
)

// Configure custom settings
domain.SetMaxConcurrentAgents(5)
domain.SetParallelTasksCount(3)
```

### Adding Custom Tools

Extend the MockMCPToolExecutor to add custom tools:

```go
func (m *MockMCPToolExecutor) ExecuteToolCall(ctx context.Context, toolCall *agent.ToolCall) (*agent.ToolResult, error) {
    switch toolCall.Function.Name {
    case "custom_analysis":
        return m.executeCustomAnalysis(ctx, toolCall)
    default:
        return m.executeDefaultTool(ctx, toolCall)
    }
}
```

## 📈 Performance Considerations

- **Concurrent Agents**: Default limit is 10 concurrent agents
- **Resource Limits**: Each agent has execution time and token limits
- **Memory Usage**: Monitor memory usage with multiple agents
- **Network**: Consider API rate limits for LLM calls

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This demo is part of the T-Rex project and follows the same license terms.
