# Server configuration
server:
  port: "8080"

# MongoDB configuration
mongodb:
  uri: "mongodb://mongouser:XekWb6w%25(%23Gq@*************:27017,**************:27017/test?authSource=admin"
  # uri: "*************************************************************"
  database: "trex_db"

# Redis configuration for session store
redis:
  address: "*************:6379"
  password: "shgdwyx703hjswu" # Keep empty if no password

# Session configuration
session:
  secret: "this-is-a-very-secret-key-please-change-me" # CHANGE THIS IN YOUR ACTUAL CONFIG

# Workspace configuration
workspace:
  base_path: "./.trex_workspaces"

# File upload configuration
file_upload:
  upload_dir: "uploads"  # Relative to workspace base_path
  max_size: 10485760     # 10MB in bytes
  max_count: 5           # Maximum 5 files per upload
  base_url: "/api/v1/uploads"  # Base URL for serving uploaded files

# MCP configuration
mcp:
  servers_config_path: "./config/mcp_servers.json"

# Logger configuration (Example, can be expanded)
logger:
  level: "debug" # e.g., debug, info, warn, error
  path: "t-rex-be.log" # Path to the log file

# Runtime configuration
runtime:
  provider: "docker" # "docker" or "kubernetes" - 切换到 "kubernetes" 来启用K8s运行时
  default_image_name: "t-rex/dev-env:latest" # 恢复使用本地镜像，因为K8s使用Docker daemon
  
  # Docker specific configuration
  docker_host: "tcp://************:2375"
  
  # Kubernetes specific configuration
  kubernetes:
    kubeconfig: "" # 空值会自动使用 ~/.kube/config (Rancher Desktop的默认配置)
    namespace: "t-rex-workspaces" # 建议使用专门的命名空间，避免与其他应用冲突
    
    # Traefik IngressRoute configuration
    traefik:
      enabled: true
      domain: "t-rex.local" # 本地开发建议使用 .local 域名，需要在 /etc/hosts 配置
      entry_points: ["web"] # Traefik entry points，本地一般用 web (HTTP)
      default_ports: [8080, 3000, 5000, 8000, 9000] # 常用的开发端口
      tls:
        enabled: false # 本地开发建议关闭HTTPS，简化配置
        secret_name: "t-rex-tls-secret" # 如果启用TLS时使用的secret名称
    
    # Storage configuration
    storage:
      class: "local-path" # Rancher Desktop默认的存储类，适合本地开发
      size: "10Gi" # 建议增加到20GB，给项目更多空间

# OpenAI configuration for Agent LLM
openai:
  # api_key: "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" # Set via environment variable
  # api_base: "https://openrouter.ai/api/v1" # Optional: for custom endpoints
  # # model_name: "deepseek/deepseek-chat-v3-0324:free"
  # model_name: "moonshotai/kimi-k2:free"

  api_base: "https://generativelanguage.googleapis.com/v1beta/openai/" # Optional: for custom endpoints
  # api_key: "AIzaSyDSua5vKziXj2P9DdNwPBzNyv6ZldWfH7E" # Set via environment variable
  # api_key: "AIzaSyBw7-2xsW0lUhdfr8zzEplUIoGY7Ki_QIc" # Set via environment variable
  api_key: "AIzaSyBnxmNNtn6gCRIdiOpBcQp6SEORzNdCg5Y" # Set via environment variable
  model_name: "gemini-2.5-pro"

#  default_image_name: "t-rex/dev-env:latest" # ARM64/Apple Silicon compatible multi-language development environment
#   docker_host: "tcp://************:2375"
#   api_key: "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" # Set via environment variable
#   api_base: "https://openrouter.ai/api/v1" # Optional: for custom endpoints
#   model_name: "anthropic/claude-sonnet-4"

tokenizer:
  encoding: "o200k_base" # Use tiktoken encoding for modern models, e.g., cl100k_base, r50k_base

# Conversation history management
history:
  max_messages: 20 # The maximum number of recent messages to keep
  max_tokens: 110000 # The maximum number of tokens for the history context 

# Google Search configuration
google_search:
  api_key: "AIzaSyBGu_l7W4I_0hUcUyDRwlUdYYCllUjExWg"
  cx: "85ea4c2e255a24005"